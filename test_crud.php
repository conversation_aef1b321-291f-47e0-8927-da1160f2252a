<?php
/**
 * CRUD Operations Test
 * 
 * This test validates that all CRUD operations work correctly.
 */

require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';
require_once 'models/User.php';
require_once 'models/Hospital.php';
require_once 'models/Department.php';
require_once 'models/Device.php';
require_once 'models/Maintenance.php';
require_once 'models/Ticket.php';
require_once 'models/Role.php';

echo "=== CRUD OPERATIONS TEST ===\n\n";

// Initialize models
$userModel = new User($pdo);
$hospitalModel = new Hospital($pdo);
$departmentModel = new Department($pdo);
$deviceModel = new Device($pdo);
$maintenanceModel = new Maintenance($pdo);
$ticketModel = new Ticket($pdo);
$roleModel = new Role($pdo);

$errors = [];
$successes = [];

// Test 1: Hospital CRUD
echo "1. Testing Hospital CRUD...\n";
try {
    // Create
    $hospitalData = [
        'name' => 'Test Hospital CRUD',
        'address' => '123 Test Street',
        'city' => 'Test City',
        'country' => 'Test Country',
        'phone' => '************',
        'email' => '<EMAIL>',
        'website' => 'https://test.com',
        'notes' => 'Test notes'
    ];
    
    $hospitalId = $hospitalModel->create($hospitalData);
    if ($hospitalId) {
        $successes[] = "Hospital CREATE: Success (ID: $hospitalId)";
        
        // Read
        $hospital = $hospitalModel->getById($hospitalId);
        if ($hospital && $hospital['name'] === $hospitalData['name']) {
            $successes[] = "Hospital READ: Success";
            
            // Update
            $updateData = $hospitalData;
            $updateData['name'] = 'Updated Test Hospital';
            $updateResult = $hospitalModel->update($hospitalId, $updateData);
            if ($updateResult) {
                $successes[] = "Hospital UPDATE: Success";
                
                // Verify update
                $updatedHospital = $hospitalModel->getById($hospitalId);
                if ($updatedHospital['name'] === 'Updated Test Hospital') {
                    $successes[] = "Hospital UPDATE verification: Success";
                } else {
                    $errors[] = "Hospital UPDATE verification: Failed";
                }
            } else {
                $errors[] = "Hospital UPDATE: Failed";
            }
            
            // Delete
            $deleteResult = $hospitalModel->delete($hospitalId);
            if ($deleteResult) {
                $successes[] = "Hospital DELETE: Success";
            } else {
                $errors[] = "Hospital DELETE: Failed";
            }
        } else {
            $errors[] = "Hospital READ: Failed";
        }
    } else {
        $errors[] = "Hospital CREATE: Failed";
    }
} catch (Exception $e) {
    $errors[] = "Hospital CRUD: Exception - " . $e->getMessage();
}

// Test 2: Department CRUD (using existing hospital ID 2)
echo "\n2. Testing Department CRUD...\n";
try {
    $deptData = [
        'hospital_id' => 2, // Use existing hospital
        'name' => 'Test Department',
        'location' => 'Floor 1',
        'phone' => '************',
        'email' => '<EMAIL>',
        'notes' => 'Test department notes'
    ];
    
    $deptId = $departmentModel->create($deptData);
    if ($deptId) {
        $successes[] = "Department CREATE: Success (ID: $deptId)";
        
        // Read
        $dept = $departmentModel->getById($deptId);
        if ($dept && $dept['name'] === $deptData['name']) {
            $successes[] = "Department READ: Success";
            
            // Update
            $updateData = $deptData;
            $updateData['name'] = 'Updated Test Department';
            $updateResult = $departmentModel->update($deptId, $updateData);
            if ($updateResult) {
                $successes[] = "Department UPDATE: Success";
            } else {
                $errors[] = "Department UPDATE: Failed";
            }
            
            // Delete (we'll keep this for device testing)
            // $departmentModel->delete($deptId);
        } else {
            $errors[] = "Department READ: Failed";
        }
    } else {
        $errors[] = "Department CREATE: Failed";
    }
} catch (Exception $e) {
    $errors[] = "Department CRUD: Exception - " . $e->getMessage();
}

// Test 3: Device CRUD (using existing hospital and new department)
echo "\n3. Testing Device CRUD...\n";
try {
    $deviceData = [
        'hospital_id' => 2,
        'department_id' => $deptId, // Use the department we just created
        'name' => 'Test Device',
        'model' => 'TEST-MODEL-001',
        'serial_number' => 'TEST-SN-' . time(),
        'manufacturer' => 'Test Manufacturer',
        'category' => 'Test Category',
        'purchase_date' => date('Y-m-d'),
        'warranty_expiry' => date('Y-m-d', strtotime('+1 year')),
        'status' => 'operational',
        'location' => 'Test Location',
        'maintenance_interval' => 30,
        'notes' => 'Test device notes'
    ];

    $deviceId = $deviceModel->create($deviceData);
    if ($deviceId) {
        $successes[] = "Device CREATE: Success (ID: $deviceId)";

        // Read
        $device = $deviceModel->getById($deviceId);
        if ($device && $device['name'] === $deviceData['name']) {
            $successes[] = "Device READ: Success";

            // Update
            $updateData = $deviceData;
            $updateData['name'] = 'Updated Test Device';
            $updateResult = $deviceModel->update($deviceId, $updateData);
            if ($updateResult) {
                $successes[] = "Device UPDATE: Success";
            } else {
                $errors[] = "Device UPDATE: Failed";
            }
        } else {
            $errors[] = "Device READ: Failed";
        }
    } else {
        $errors[] = "Device CREATE: Failed";
    }
} catch (Exception $e) {
    $errors[] = "Device CRUD: Exception - " . $e->getMessage();
}

// Test 4: User CRUD
echo "\n4. Testing User CRUD...\n";
try {
    $userData = [
        'username' => 'testuser_' . time(),
        'password' => 'TestPassword123!',
        'email' => '<EMAIL>',
        'full_name' => 'Test User',
        'role' => 'technician',
        'hospital_id' => 2,
        'language' => 'en',
        'status' => 'active'
    ];

    $userId = $userModel->create($userData);
    if ($userId) {
        $successes[] = "User CREATE: Success (ID: $userId)";

        // Read
        $user = $userModel->getById($userId);
        if ($user && $user['username'] === $userData['username']) {
            $successes[] = "User READ: Success";

            // Update
            $updateData = [
                'email' => '<EMAIL>',
                'full_name' => 'Updated Test User',
                'role' => 'technician',
                'hospital_id' => 2,
                'language' => 'en',
                'status' => 'active'
            ];
            $updateResult = $userModel->update($userId, $updateData);
            if ($updateResult) {
                $successes[] = "User UPDATE: Success";
            } else {
                $errors[] = "User UPDATE: Failed";
            }

            // Delete
            $deleteResult = $userModel->delete($userId);
            if ($deleteResult) {
                $successes[] = "User DELETE: Success";
            } else {
                $errors[] = "User DELETE: Failed";
            }
        } else {
            $errors[] = "User READ: Failed";
        }
    } else {
        $errors[] = "User CREATE: Failed";
    }
} catch (Exception $e) {
    $errors[] = "User CRUD: Exception - " . $e->getMessage();
}

// Test 5: Ticket CRUD
echo "\n5. Testing Ticket CRUD...\n";
try {
    $ticketData = [
        'device_id' => isset($deviceId) ? $deviceId : null,
        'reported_by' => 1, // Admin user
        'title' => 'Test Ticket',
        'description' => 'This is a test ticket',
        'priority' => 'medium',
        'status' => 'open'
    ];

    $ticketId = $ticketModel->create($ticketData);
    if ($ticketId) {
        $successes[] = "Ticket CREATE: Success (ID: $ticketId)";

        // Read
        $ticket = $ticketModel->getById($ticketId);
        if ($ticket && $ticket['title'] === $ticketData['title']) {
            $successes[] = "Ticket READ: Success";

            // Update
            $updateData = $ticketData;
            $updateData['status'] = 'in_progress';
            $updateResult = $ticketModel->update($ticketId, $updateData);
            if ($updateResult) {
                $successes[] = "Ticket UPDATE: Success";
            } else {
                $errors[] = "Ticket UPDATE: Failed";
            }

            // Delete
            $deleteResult = $ticketModel->delete($ticketId);
            if ($deleteResult) {
                $successes[] = "Ticket DELETE: Success";
            } else {
                $errors[] = "Ticket DELETE: Failed";
            }
        } else {
            $errors[] = "Ticket READ: Failed";
        }
    } else {
        $errors[] = "Ticket CREATE: Failed";
    }
} catch (Exception $e) {
    $errors[] = "Ticket CRUD: Exception - " . $e->getMessage();
}

// Clean up test data
if (isset($deviceId)) {
    $deviceModel->delete($deviceId);
}
if (isset($deptId)) {
    $departmentModel->delete($deptId);
}

echo "\n=== RESULTS ===\n";
echo "Successes (" . count($successes) . "):\n";
foreach ($successes as $success) {
    echo "✓ $success\n";
}

if (!empty($errors)) {
    echo "\nErrors (" . count($errors) . "):\n";
    foreach ($errors as $error) {
        echo "✗ $error\n";
    }
} else {
    echo "\n🎉 All CRUD operations working correctly!\n";
}
