<?php
require_once 'config/database.php';

echo "=== DATABASE STATUS CHECK ===\n";

try {
    // Check hospitals
    $stmt = $pdo->query('SELECT COUNT(*) FROM hospitals');
    $hospitalCount = $stmt->fetchColumn();
    echo "Hospitals: $hospitalCount\n";
    
    if ($hospitalCount > 0) {
        $stmt = $pdo->query('SELECT id, name FROM hospitals LIMIT 3');
        $hospitals = $stmt->fetchAll();
        foreach ($hospitals as $hospital) {
            echo "  - ID: {$hospital['id']}, Name: {$hospital['name']}\n";
        }
    }
    
    // Check departments
    $stmt = $pdo->query('SELECT COUNT(*) FROM departments');
    $deptCount = $stmt->fetchColumn();
    echo "\nDepartments: $deptCount\n";
    
    if ($deptCount > 0) {
        $stmt = $pdo->query('SELECT id, name, hospital_id FROM departments LIMIT 3');
        $departments = $stmt->fetchAll();
        foreach ($departments as $dept) {
            echo "  - ID: {$dept['id']}, Name: {$dept['name']}, Hospital: {$dept['hospital_id']}\n";
        }
    }
    
    // Check devices
    $stmt = $pdo->query('SELECT COUNT(*) FROM devices');
    $deviceCount = $stmt->fetchColumn();
    echo "\nDevices: $deviceCount\n";
    
    if ($deviceCount > 0) {
        $stmt = $pdo->query('SELECT id, name, hospital_id, department_id FROM devices LIMIT 3');
        $devices = $stmt->fetchAll();
        foreach ($devices as $device) {
            echo "  - ID: {$device['id']}, Name: {$device['name']}, Hospital: {$device['hospital_id']}, Dept: {$device['department_id']}\n";
        }
    }
    
    // Check users
    $stmt = $pdo->query('SELECT COUNT(*) FROM users');
    $userCount = $stmt->fetchColumn();
    echo "\nUsers: $userCount\n";
    
    if ($userCount > 0) {
        $stmt = $pdo->query('SELECT id, username, role FROM users LIMIT 3');
        $users = $stmt->fetchAll();
        foreach ($users as $user) {
            echo "  - ID: {$user['id']}, Username: {$user['username']}, Role: {$user['role']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
