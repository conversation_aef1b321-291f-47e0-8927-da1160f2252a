<?php
/**
 * Web Interface Logic Test
 * 
 * This test simulates web requests to check for logic errors.
 */

// Start session
session_start();

require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';
require_once 'includes/security.php';
require_once 'models/User.php';
require_once 'models/Hospital.php';
require_once 'models/Department.php';
require_once 'models/Device.php';

echo "=== WEB INTERFACE LOGIC TEST ===\n\n";

// Initialize models
$userModel = new User($pdo);
$hospitalModel = new Hospital($pdo);
$departmentModel = new Department($pdo);
$deviceModel = new Device($pdo);

$errors = [];
$successes = [];

// Test 1: Authentication Logic
echo "1. Testing Authentication Logic...\n";
try {
    // Test valid login
    $loginResult = authenticate('admin', 'admin123');
    if ($loginResult) {
        $successes[] = "Authentication: Valid login successful";
        
        // Check if user session is set correctly
        if (isset($_SESSION['user']) && $_SESSION['user']['username'] === 'admin') {
            $successes[] = "Authentication: Session set correctly";
        } else {
            $errors[] = "Authentication: Session not set correctly";
        }
    } else {
        $errors[] = "Authentication: Valid login failed";
    }
    
    // Test invalid login
    $invalidLogin = authenticate('admin', 'wrongpassword');
    if (!$invalidLogin) {
        $successes[] = "Authentication: Invalid login correctly rejected";
    } else {
        $errors[] = "Authentication: Invalid login incorrectly accepted";
    }
    
} catch (Exception $e) {
    $errors[] = "Authentication: Exception - " . $e->getMessage();
}

// Test 2: Permission System
echo "\n2. Testing Permission System...\n";
try {
    // Test admin permissions
    if (hasPermission('manage_users')) {
        $successes[] = "Permissions: Admin has manage_users permission";
    } else {
        $errors[] = "Permissions: Admin missing manage_users permission";
    }
    
    if (hasPermission('manage_devices')) {
        $successes[] = "Permissions: Admin has manage_devices permission";
    } else {
        $errors[] = "Permissions: Admin missing manage_devices permission";
    }
    
    // Test non-existent permission
    if (!hasPermission('non_existent_permission')) {
        $successes[] = "Permissions: Non-existent permission correctly denied";
    } else {
        $errors[] = "Permissions: Non-existent permission incorrectly granted";
    }
    
} catch (Exception $e) {
    $errors[] = "Permissions: Exception - " . $e->getMessage();
}

// Test 3: Form Validation Logic
echo "\n3. Testing Form Validation Logic...\n";
try {
    // Test hospital form validation
    $hospitalData = [
        'name' => '', // Empty name should fail
        'address' => 'Test Address',
        'city' => 'Test City'
    ];
    
    $errors_found = [];
    if (empty($hospitalData['name'])) {
        $errors_found['name'] = 'Name is required';
    }
    
    if (!empty($errors_found)) {
        $successes[] = "Validation: Hospital name validation working";
    } else {
        $errors[] = "Validation: Hospital name validation not working";
    }
    
    // Test device form validation
    $deviceData = [
        'name' => 'Test Device',
        'serial_number' => '', // Empty serial should fail
        'hospital_id' => 2
    ];
    
    $device_errors = [];
    if (empty($deviceData['serial_number'])) {
        $device_errors['serial_number'] = 'Serial number is required';
    }
    
    if (!empty($device_errors)) {
        $successes[] = "Validation: Device serial number validation working";
    } else {
        $errors[] = "Validation: Device serial number validation not working";
    }
    
} catch (Exception $e) {
    $errors[] = "Validation: Exception - " . $e->getMessage();
}

// Test 4: Data Sanitization
echo "\n4. Testing Data Sanitization...\n";
try {
    $maliciousInput = '<script>alert("xss")</script>';
    $sanitized = sanitize($maliciousInput);
    
    if (strpos($sanitized, '<script>') === false) {
        $successes[] = "Sanitization: XSS attempt blocked";
    } else {
        $errors[] = "Sanitization: XSS attempt not blocked";
    }
    
    $sqlInjection = "'; DROP TABLE users; --";
    $sanitizedSql = sanitizeForDatabase($sqlInjection);
    
    if (strpos($sanitizedSql, 'DROP TABLE') === false) {
        $successes[] = "Sanitization: SQL injection attempt blocked";
    } else {
        $errors[] = "Sanitization: SQL injection attempt not blocked";
    }
    
} catch (Exception $e) {
    $errors[] = "Sanitization: Exception - " . $e->getMessage();
}

// Test 5: URL Routing Logic
echo "\n5. Testing URL Routing Logic...\n";
try {
    // Simulate URL parsing
    $testUrls = [
        'devices/create' => ['controller' => 'devices', 'action' => 'create'],
        'users/edit/1' => ['controller' => 'users', 'action' => 'edit', 'param' => '1'],
        'dashboard' => ['controller' => 'dashboard', 'action' => 'index'],
        '' => ['controller' => 'dashboard', 'action' => 'index']
    ];
    
    foreach ($testUrls as $url => $expected) {
        $urlParts = explode('/', $url);
        $controller = !empty($urlParts[0]) ? $urlParts[0] : 'dashboard';
        $action = isset($urlParts[1]) ? $urlParts[1] : 'index';
        $param = isset($urlParts[2]) ? $urlParts[2] : null;
        
        if ($controller === $expected['controller'] && $action === $expected['action']) {
            $successes[] = "Routing: URL '$url' parsed correctly";
        } else {
            $errors[] = "Routing: URL '$url' parsed incorrectly";
        }
    }
    
} catch (Exception $e) {
    $errors[] = "Routing: Exception - " . $e->getMessage();
}

echo "\n=== RESULTS ===\n";
echo "Successes (" . count($successes) . "):\n";
foreach ($successes as $success) {
    echo "✓ $success\n";
}

if (!empty($errors)) {
    echo "\nErrors (" . count($errors) . "):\n";
    foreach ($errors as $error) {
        echo "✗ $error\n";
    }
} else {
    echo "\n🎉 All web interface logic working correctly!\n";
}
