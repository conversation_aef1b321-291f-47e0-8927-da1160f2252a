<?php
/**
 * Comprehensive Error Check
 * 
 * This script checks for all types of errors, logic issues, and potential problems.
 */

require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';
require_once 'includes/security.php';

echo "=== COMPREHENSIVE ERROR ANALYSIS ===\n\n";

$issues = [];
$warnings = [];
$suggestions = [];

// 1. Check for missing files and dependencies
echo "1. Checking File Dependencies...\n";

$requiredFiles = [
    'config/database.php',
    'includes/functions.php',
    'includes/auth.php',
    'includes/security.php',
    'includes/notifications.php',
    'includes/email.php',
    'includes/export.php',
    'models/User.php',
    'models/Hospital.php',
    'models/Department.php',
    'models/Device.php',
    'models/Maintenance.php',
    'models/Ticket.php',
    'models/Role.php'
];

foreach ($requiredFiles as $file) {
    if (!file_exists($file)) {
        $issues[] = "Missing required file: $file";
    } else {
        // Check for syntax errors
        $output = shell_exec("C:\\xampp\\php\\php.exe -l \"$file\" 2>&1");
        if (strpos($output, 'No syntax errors') === false) {
            $issues[] = "Syntax error in $file: " . trim($output);
        }
    }
}

// 2. Check database connection and schema
echo "\n2. Checking Database Schema...\n";

try {
    // Check if all required tables exist
    $requiredTables = [
        'users', 'hospitals', 'departments', 'devices', 'maintenance_schedules',
        'maintenance_logs', 'tickets', 'roles', 'activity_logs', 'notifications',
        'password_resets', 'remember_tokens'
    ];
    
    $stmt = $pdo->query("SHOW TABLES");
    $existingTables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($requiredTables as $table) {
        if (!in_array($table, $existingTables)) {
            $issues[] = "Missing database table: $table";
        }
    }
    
    // Check for foreign key constraints
    $stmt = $pdo->query("
        SELECT TABLE_NAME, CONSTRAINT_NAME 
        FROM information_schema.TABLE_CONSTRAINTS 
        WHERE CONSTRAINT_TYPE = 'FOREIGN KEY' 
        AND TABLE_SCHEMA = DATABASE()
    ");
    $foreignKeys = $stmt->fetchAll();
    
    if (count($foreignKeys) < 10) {
        $warnings[] = "Low number of foreign key constraints detected. Expected at least 10, found " . count($foreignKeys);
    }
    
} catch (Exception $e) {
    $issues[] = "Database schema check failed: " . $e->getMessage();
}

// 3. Check for security vulnerabilities
echo "\n3. Checking Security Issues...\n";

// Check if CSRF protection is implemented
$controllerFiles = glob('controllers/*.php');
foreach ($controllerFiles as $file) {
    $content = file_get_contents($file);
    
    // Check for POST handling without CSRF protection
    if (strpos($content, '$_POST') !== false && strpos($content, 'csrf_token') === false) {
        $warnings[] = "Potential missing CSRF protection in: " . basename($file);
    }
    
    // Check for direct SQL queries (should use prepared statements)
    if (preg_match('/\$pdo->query\s*\(\s*["\'].*\$/', $content)) {
        $issues[] = "Potential SQL injection vulnerability in: " . basename($file);
    }
    
    // Check for unescaped output
    if (preg_match('/echo\s+\$_[A-Z]+/', $content)) {
        $warnings[] = "Potential XSS vulnerability (unescaped output) in: " . basename($file);
    }
}

// 4. Check for logic errors in models
echo "\n4. Checking Model Logic...\n";

$modelFiles = glob('models/*.php');
foreach ($modelFiles as $file) {
    $content = file_get_contents($file);
    
    // Check for missing error handling in database operations
    if (strpos($content, 'prepare(') !== false && strpos($content, 'try {') === false) {
        $warnings[] = "Missing error handling in: " . basename($file);
    }
    
    // Check for hardcoded values
    if (preg_match('/WHERE\s+id\s*=\s*[0-9]+/', $content)) {
        $warnings[] = "Hardcoded ID values found in: " . basename($file);
    }
}

// 5. Check for authentication and authorization issues
echo "\n5. Checking Authentication Logic...\n";

session_start();

// Test authentication bypass
if (!function_exists('hasPermission')) {
    $issues[] = "hasPermission function not found - critical security function missing";
}

if (!function_exists('requirePermission')) {
    $issues[] = "requirePermission function not found - critical security function missing";
}

if (!function_exists('requireLogin')) {
    $issues[] = "requireLogin function not found - critical security function missing";
}

// 6. Check for configuration issues
echo "\n6. Checking Configuration...\n";

// Check if debug mode is enabled in production
if (defined('DEBUG') && DEBUG === true) {
    $warnings[] = "DEBUG mode is enabled - should be disabled in production";
}

// Check database credentials
if (DB_USER === 'root' && DB_PASS === '') {
    $warnings[] = "Using default database credentials - security risk in production";
}

// Check for missing PHP extensions
$requiredExtensions = ['pdo', 'pdo_mysql', 'json', 'session'];
foreach ($requiredExtensions as $ext) {
    if (!extension_loaded($ext)) {
        $issues[] = "Missing required PHP extension: $ext";
    }
}

// Optional but recommended extensions
$recommendedExtensions = ['gd', 'openssl', 'mbstring'];
foreach ($recommendedExtensions as $ext) {
    if (!extension_loaded($ext)) {
        $suggestions[] = "Recommended PHP extension not loaded: $ext";
    }
}

// 7. Check for file permissions and security
echo "\n7. Checking File Security...\n";

// Check if sensitive files are accessible
$sensitiveFiles = ['config/database.php', '.env'];
foreach ($sensitiveFiles as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        if ($perms & 0x0004) { // World readable
            $warnings[] = "Sensitive file $file is world-readable";
        }
    }
}

// Check upload directory
if (!is_dir('uploads')) {
    $warnings[] = "Upload directory 'uploads' does not exist";
} elseif (!is_writable('uploads')) {
    $issues[] = "Upload directory 'uploads' is not writable";
}

// 8. Print Results
echo "\n=== ANALYSIS RESULTS ===\n";

if (!empty($issues)) {
    echo "\n🔴 CRITICAL ISSUES (" . count($issues) . "):\n";
    foreach ($issues as $issue) {
        echo "   ✗ $issue\n";
    }
}

if (!empty($warnings)) {
    echo "\n🟡 WARNINGS (" . count($warnings) . "):\n";
    foreach ($warnings as $warning) {
        echo "   ⚠ $warning\n";
    }
}

if (!empty($suggestions)) {
    echo "\n🔵 SUGGESTIONS (" . count($suggestions) . "):\n";
    foreach ($suggestions as $suggestion) {
        echo "   💡 $suggestion\n";
    }
}

if (empty($issues) && empty($warnings)) {
    echo "\n🎉 NO CRITICAL ISSUES OR WARNINGS FOUND!\n";
    echo "The system appears to be well-configured and secure.\n";
} else {
    echo "\nSUMMARY:\n";
    echo "- Critical Issues: " . count($issues) . "\n";
    echo "- Warnings: " . count($warnings) . "\n";
    echo "- Suggestions: " . count($suggestions) . "\n";
}

echo "\n=== END OF ANALYSIS ===\n";
