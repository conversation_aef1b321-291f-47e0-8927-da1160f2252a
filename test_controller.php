<?php
/**
 * Controller Test
 * 
 * Test controller functionality directly.
 */

// Start session first
session_start();

require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';
require_once 'includes/security.php';
require_once 'models/User.php';
require_once 'models/Hospital.php';
require_once 'models/Department.php';
require_once 'models/Device.php';

echo "=== CONTROLLER TEST ===\n\n";

// Initialize models
$userModel = new User($pdo);
$hospitalModel = new Hospital($pdo);
$departmentModel = new Department($pdo);
$deviceModel = new Device($pdo);

// Simulate login
$_SESSION['user'] = [
    'id' => 1,
    'username' => 'admin',
    'role' => 'admin',
    'permissions' => ['manage_users', 'manage_hospitals', 'manage_devices'],
    'hospital_id' => 2
];

echo "1. Testing Hospital Controller Logic...\n";

// Simulate POST request for hospital creation
$_POST = [
    'name' => 'Test Hospital Controller',
    'address' => '123 Test St',
    'city' => 'Test City',
    'country' => 'Test Country',
    'phone' => '************',
    'email' => '<EMAIL>'
];

try {
    // Simulate hospital creation logic from controller
    $hospitalData = [
        'name' => $_POST['name'],
        'address' => $_POST['address'] ?? '',
        'city' => $_POST['city'] ?? '',
        'country' => $_POST['country'] ?? '',
        'phone' => $_POST['phone'] ?? '',
        'email' => $_POST['email'] ?? '',
        'website' => $_POST['website'] ?? '',
        'notes' => $_POST['notes'] ?? ''
    ];
    
    // Validation
    $errors = [];
    if (empty($hospitalData['name'])) {
        $errors['name'] = 'Name is required';
    }
    
    if (empty($errors)) {
        $hospitalId = $hospitalModel->create($hospitalData);
        if ($hospitalId) {
            echo "✓ Hospital creation successful (ID: $hospitalId)\n";
            
            // Clean up
            $hospitalModel->delete($hospitalId);
        } else {
            echo "✗ Hospital creation failed\n";
        }
    } else {
        echo "✗ Hospital validation failed\n";
    }
} catch (Exception $e) {
    echo "✗ Hospital controller error: " . $e->getMessage() . "\n";
}

echo "\n2. Testing Device Controller Logic...\n";

// First create a department for the device
$deptData = [
    'hospital_id' => 2,
    'name' => 'Test Department for Device',
    'location' => 'Floor 1'
];
$deptId = $departmentModel->create($deptData);

if ($deptId) {
    // Simulate POST request for device creation
    $_POST = [
        'hospital_id' => '2',
        'department_id' => (string)$deptId,
        'name' => 'Test Device Controller',
        'model' => 'TEST-MODEL',
        'serial_number' => 'TEST-SN-' . time(),
        'manufacturer' => 'Test Manufacturer',
        'category' => 'Test',
        'purchase_date' => date('Y-m-d'),
        'warranty_expiry' => date('Y-m-d', strtotime('+1 year')),
        'status' => 'operational'
    ];
    
    try {
        // Simulate device creation logic from controller
        $deviceData = [
            'hospital_id' => (int)$_POST['hospital_id'],
            'department_id' => (int)$_POST['department_id'],
            'name' => $_POST['name'],
            'model' => $_POST['model'],
            'serial_number' => $_POST['serial_number'],
            'manufacturer' => $_POST['manufacturer'],
            'category' => $_POST['category'],
            'purchase_date' => $_POST['purchase_date'],
            'warranty_expiry' => $_POST['warranty_expiry'],
            'status' => $_POST['status'],
            'location' => $_POST['location'] ?? '',
            'maintenance_interval' => (int)($_POST['maintenance_interval'] ?? 30),
            'notes' => $_POST['notes'] ?? ''
        ];
        
        // Validation
        $errors = [];
        if (empty($deviceData['name'])) {
            $errors['name'] = 'Name is required';
        }
        if (empty($deviceData['serial_number'])) {
            $errors['serial_number'] = 'Serial number is required';
        }
        
        if (empty($errors)) {
            $deviceId = $deviceModel->create($deviceData);
            if ($deviceId) {
                echo "✓ Device creation successful (ID: $deviceId)\n";
                
                // Clean up
                $deviceModel->delete($deviceId);
            } else {
                echo "✗ Device creation failed\n";
            }
        } else {
            echo "✗ Device validation failed\n";
        }
    } catch (Exception $e) {
        echo "✗ Device controller error: " . $e->getMessage() . "\n";
    }
    
    // Clean up department
    $departmentModel->delete($deptId);
} else {
    echo "✗ Could not create test department for device test\n";
}

echo "\n3. Testing User Controller Logic...\n";

// Simulate POST request for user creation
$_POST = [
    'username' => 'testuser_' . time(),
    'password' => 'TestPassword123!',
    'email' => '<EMAIL>',
    'full_name' => 'Test User Controller',
    'role' => 'technician',
    'hospital_id' => '2'
];

try {
    // Simulate user creation logic from controller
    $userData = [
        'username' => $_POST['username'],
        'password' => $_POST['password'],
        'email' => $_POST['email'],
        'full_name' => $_POST['full_name'],
        'role' => $_POST['role'],
        'hospital_id' => (int)$_POST['hospital_id'],
        'language' => $_POST['language'] ?? 'en',
        'status' => $_POST['status'] ?? 'active'
    ];
    
    // Validation
    $errors = [];
    if (empty($userData['username'])) {
        $errors['username'] = 'Username is required';
    }
    if (empty($userData['password'])) {
        $errors['password'] = 'Password is required';
    }
    if (empty($userData['email'])) {
        $errors['email'] = 'Email is required';
    }
    
    if (empty($errors)) {
        $userId = $userModel->create($userData);
        if ($userId) {
            echo "✓ User creation successful (ID: $userId)\n";
            
            // Clean up
            $userModel->delete($userId);
        } else {
            echo "✗ User creation failed\n";
        }
    } else {
        echo "✗ User validation failed\n";
    }
} catch (Exception $e) {
    echo "✗ User controller error: " . $e->getMessage() . "\n";
}

echo "\n🎉 Controller tests completed!\n";
