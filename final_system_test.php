<?php
/**
 * Final System Test
 * 
 * Comprehensive test of all system components.
 */

require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';
require_once 'includes/security.php';
require_once 'models/User.php';
require_once 'models/Hospital.php';
require_once 'models/Department.php';
require_once 'models/Device.php';
require_once 'models/Maintenance.php';
require_once 'models/Ticket.php';
require_once 'models/Role.php';

echo "=== FINAL SYSTEM TEST ===\n\n";

$results = [
    'database_connection' => false,
    'models_loaded' => false,
    'authentication' => false,
    'permissions' => false,
    'crud_operations' => false,
    'data_validation' => false,
    'security_functions' => false
];

// Test 1: Database Connection
echo "1. Testing Database Connection...\n";
try {
    $stmt = $pdo->query("SELECT 1");
    if ($stmt) {
        $results['database_connection'] = true;
        echo "✓ Database connection successful\n";
    }
} catch (Exception $e) {
    echo "✗ Database connection failed: " . $e->getMessage() . "\n";
}

// Test 2: Models Loading
echo "\n2. Testing Models Loading...\n";
try {
    $userModel = new User($pdo);
    $hospitalModel = new Hospital($pdo);
    $departmentModel = new Department($pdo);
    $deviceModel = new Device($pdo);
    $maintenanceModel = new Maintenance($pdo);
    $ticketModel = new Ticket($pdo);
    $roleModel = new Role($pdo);
    
    $results['models_loaded'] = true;
    echo "✓ All models loaded successfully\n";
} catch (Exception $e) {
    echo "✗ Model loading failed: " . $e->getMessage() . "\n";
}

// Test 3: Authentication System
echo "\n3. Testing Authentication System...\n";
try {
    session_start();
    
    // Test valid login
    $loginResult = authenticate('admin', 'admin123');
    if ($loginResult && isset($_SESSION['user'])) {
        $results['authentication'] = true;
        echo "✓ Authentication system working\n";
    } else {
        echo "✗ Authentication failed\n";
    }
} catch (Exception $e) {
    echo "✗ Authentication error: " . $e->getMessage() . "\n";
}

// Test 4: Permission System
echo "\n4. Testing Permission System...\n";
try {
    if (hasPermission('manage_users') && hasPermission('manage_devices')) {
        $results['permissions'] = true;
        echo "✓ Permission system working\n";
    } else {
        echo "✗ Permission system failed\n";
    }
} catch (Exception $e) {
    echo "✗ Permission error: " . $e->getMessage() . "\n";
}

// Test 5: Basic CRUD Operations
echo "\n5. Testing CRUD Operations...\n";
try {
    // Test hospital CRUD
    $hospitalData = [
        'name' => 'Test Hospital Final',
        'address' => 'Test Address',
        'city' => 'Test City',
        'country' => 'Test Country'
    ];
    
    $hospitalId = $hospitalModel->create($hospitalData);
    $hospital = $hospitalModel->getById($hospitalId);
    $updateResult = $hospitalModel->update($hospitalId, ['name' => 'Updated Hospital']);
    $deleteResult = $hospitalModel->delete($hospitalId);
    
    if ($hospitalId && $hospital && $updateResult && $deleteResult) {
        $results['crud_operations'] = true;
        echo "✓ CRUD operations working\n";
    } else {
        echo "✗ CRUD operations failed\n";
    }
} catch (Exception $e) {
    echo "✗ CRUD error: " . $e->getMessage() . "\n";
}

// Test 6: Data Validation
echo "\n6. Testing Data Validation...\n";
try {
    // Test sanitization
    $maliciousInput = '<script>alert("test")</script>';
    $sanitized = sanitize($maliciousInput);
    
    // Test validation logic
    $errors = [];
    if (empty('')) {
        $errors[] = 'Empty field validation working';
    }
    
    if (strpos($sanitized, '<script>') === false && !empty($errors)) {
        $results['data_validation'] = true;
        echo "✓ Data validation working\n";
    } else {
        echo "✗ Data validation failed\n";
    }
} catch (Exception $e) {
    echo "✗ Validation error: " . $e->getMessage() . "\n";
}

// Test 7: Security Functions
echo "\n7. Testing Security Functions...\n";
try {
    $csrfToken = generateCSRFToken();
    $isValid = verifyCSRFToken($csrfToken);
    $clientIp = getClientIp();
    
    if ($csrfToken && $isValid && $clientIp) {
        $results['security_functions'] = true;
        echo "✓ Security functions working\n";
    } else {
        echo "✗ Security functions failed\n";
    }
} catch (Exception $e) {
    echo "✗ Security error: " . $e->getMessage() . "\n";
}

// Final Results
echo "\n=== FINAL RESULTS ===\n";
$passed = array_sum($results);
$total = count($results);

foreach ($results as $test => $result) {
    $status = $result ? '✓' : '✗';
    $testName = ucwords(str_replace('_', ' ', $test));
    echo "$status $testName\n";
}

echo "\nOverall: $passed/$total tests passed\n";

if ($passed === $total) {
    echo "\n🎉 ALL SYSTEMS OPERATIONAL!\n";
    echo "The Medical Device Management System is working correctly.\n";
} else {
    echo "\n⚠️ Some systems need attention.\n";
    echo "Please check the failed tests above.\n";
}
